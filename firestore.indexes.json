{"indexes": [{"collectionGroup": "diagnostic_events", "queryScope": "COLLECTION", "fields": [{"fieldPath": "completed", "order": "ASCENDING"}, {"fieldPath": "nextActionDate", "order": "ASCENDING"}]}, {"collectionGroup": "diagnostic_events", "queryScope": "COLLECTION", "fields": [{"fieldPath": "plantId", "order": "ASCENDING"}, {"fieldPath": "nextActionDate", "order": "ASCENDING"}]}, {"collectionGroup": "diagnostic_events", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "nextActionDate", "order": "ASCENDING"}]}, {"collectionGroup": "diagnostic_events", "queryScope": "COLLECTION", "fields": [{"fieldPath": "priority", "order": "ASCENDING"}, {"fieldPath": "nextActionDate", "order": "ASCENDING"}]}, {"collectionGroup": "diagnostic_events", "queryScope": "COLLECTION", "fields": [{"fieldPath": "eventType", "order": "ASCENDING"}, {"fieldPath": "nextActionDate", "order": "ASCENDING"}]}, {"collectionGroup": "notifications", "queryScope": "COLLECTION", "fields": [{"fieldPath": "read", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "action_history", "queryScope": "COLLECTION", "fields": [{"fieldPath": "plantId", "order": "ASCENDING"}, {"fieldPath": "actionDate", "order": "DESCENDING"}]}, {"collectionGroup": "diagnostics", "queryScope": "COLLECTION", "fields": [{"fieldPath": "timestamp", "order": "ASCENDING"}]}, {"collectionGroup": "diagnostics", "queryScope": "COLLECTION", "fields": [{"fieldPath": "timestamp", "order": "DESCENDING"}]}, {"collectionGroup": "plants", "queryScope": "COLLECTION", "fields": [{"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "diagnostic_records", "queryScope": "COLLECTION", "fields": [{"fieldPath": "plantId", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "DESCENDING"}]}, {"collectionGroup": "archives", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "year", "order": "DESCENDING"}]}], "fieldOverrides": []}