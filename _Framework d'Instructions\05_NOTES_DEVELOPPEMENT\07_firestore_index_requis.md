# 🔍 Index Firestore Requis - Violet Rikita

## 📊 Problème Identifié

L'erreur suivante apparaît dans la console :
```
The query requires an index. You can create it here: https://console.firebase.google.com/v1/r/project/florasynth-a461d/firestore/indexes?create_composite=...
```

## 🎯 Index Composite Requis

### **1. Collection `diagnostic_events`**

**Champs à indexer :**
- `completed` (Ascending)
- `nextActionDate` (Ascending) 
- `__name__` (Ascending)

**Requête concernée :**
```typescript
// Dans notificationService.ts ligne 226
const q = query(
  collection(db, `users/${userId}/diagnostic_events`),
  where('completed', '==', false),
  orderBy('nextActionDate', 'asc'),
  limit(50)
);
```

## 🔗 Liens de Création Automatique

### **Lien Direct Firebase Console**
```
https://console.firebase.google.com/v1/r/project/florasynth-a461d/firestore/indexes?create_composite=Clpwcm9qZWN0cy9mbG9yYXN5bnRoLWE0NjFkL2RhdGFiYXNlcy8oZGVmYXVsdCkvY29sbGVjdGlvbkdyb3Vwcy9kaWFnbm9zdGljX2V2ZW50cy9pbmRleGVzL18QARoNCgljb21wbGV0ZWQQARoSCg5uZXh0QWN0aW9uRGF0ZRABGgwKCF9fbmFtZV9fEAE
```

## ⚡ Actions à Effectuer

### **Étape 1 : Accéder à Firebase Console**
1. Ouvrir le lien ci-dessus
2. Se connecter avec le compte Google du projet
3. Confirmer la création de l'index

### **Étape 2 : Vérification**
- L'index sera créé automatiquement
- Temps de création : 2-5 minutes
- Status visible dans l'onglet "Indexes"

### **Étape 3 : Test**
- Recharger l'application
- Vérifier que l'erreur d'index a disparu
- Les notifications devraient se charger sans erreur

## 🛡️ Sécurité

**Règles Firestore Mises à Jour :**
- ✅ Collection `archives` ajoutée
- ✅ Validation du format `userId_year`
- ✅ Accès limité au propriétaire

## 📝 Notes Techniques

### **Pourquoi cet Index ?**
- La requête combine `where()` et `orderBy()` sur des champs différents
- Firestore nécessite un index composite pour ce type de requête
- L'index améliore les performances des notifications

### **Impact Performance**
- ⚡ Requêtes plus rapides
- 📉 Réduction de la latence
- 🔄 Moins d'erreurs de timeout

## ✅ Checklist de Validation

- [ ] Index créé dans Firebase Console
- [ ] Règles Firestore déployées
- [ ] Application rechargée
- [ ] Erreurs de console vérifiées
- [ ] Fonctionnalités d'archivage testées
- [ ] Notifications fonctionnelles

---

**Date de création :** 2025-07-25  
**Statut :** 🔄 En cours de résolution  
**Priorité :** 🔥 Critique
