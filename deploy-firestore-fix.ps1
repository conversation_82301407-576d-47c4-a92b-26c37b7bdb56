# 🔧 Script de Correction Firestore - Violet Rikita
# Auteur: Agent Assistant
# Date: 2025-07-26

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "🔧 CORRECTION FIRESTORE - VIOLET RIKITA" -ForegroundColor Cyan  
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Fonction pour afficher les messages avec couleurs
function Write-Success($message) {
    Write-Host "✅ $message" -ForegroundColor Green
}

function Write-Error($message) {
    Write-Host "❌ $message" -ForegroundColor Red
}

function Write-Info($message) {
    Write-Host "📊 $message" -ForegroundColor Yellow
}

function Write-Warning($message) {
    Write-Host "⚠️ $message" -ForegroundColor Magenta
}

# Étape 1: Vérification des fichiers
Write-Info "Étape 1: Vérification des fichiers..."

if (-not (Test-Path "firestore.indexes.json")) {
    Write-Error "firestore.indexes.json introuvable"
    Read-Host "Appuyez sur Entrée pour quitter"
    exit 1
}

if (-not (Test-Path "firestore.rules")) {
    Write-Error "firestore.rules introuvable"
    Read-Host "Appuyez sur Entrée pour quitter"
    exit 1
}

Write-Success "Fichiers trouvés"

# Étape 2: Vérification Firebase CLI
Write-Info "Étape 2: Vérification Firebase CLI..."

try {
    $firebaseVersion = firebase --version 2>$null
    if ($LASTEXITCODE -ne 0) {
        throw "Firebase CLI non trouvé"
    }
    Write-Success "Firebase CLI configuré - Version: $firebaseVersion"
} catch {
    Write-Error "Firebase CLI non installé ou non configuré"
    Write-Host "Installez Firebase CLI: npm install -g firebase-tools" -ForegroundColor White
    Write-Host "Connectez-vous: firebase login" -ForegroundColor White
    Read-Host "Appuyez sur Entrée pour quitter"
    exit 1
}

# Étape 3: Validation JSON
Write-Info "Étape 3: Validation de la syntaxe JSON..."

try {
    $indexContent = Get-Content "firestore.indexes.json" -Raw | ConvertFrom-Json
    Write-Success "Syntaxe JSON valide pour firestore.indexes.json"
} catch {
    Write-Error "Erreur de syntaxe dans firestore.indexes.json: $($_.Exception.Message)"
    Read-Host "Appuyez sur Entrée pour quitter"
    exit 1
}

# Étape 4: Déploiement des règles
Write-Info "Étape 4: Déploiement des règles Firestore..."

try {
    $rulesResult = firebase deploy --only firestore:rules 2>&1
    if ($LASTEXITCODE -ne 0) {
        throw "Erreur lors du déploiement des règles"
    }
    Write-Success "Règles Firestore déployées"
} catch {
    Write-Error "Erreur lors du déploiement des règles: $($_.Exception.Message)"
    Write-Host $rulesResult -ForegroundColor Red
    Read-Host "Appuyez sur Entrée pour continuer quand même"
}

# Étape 5: Déploiement des index
Write-Info "Étape 5: Déploiement des index Firestore..."

try {
    $indexResult = firebase deploy --only firestore:indexes 2>&1
    if ($LASTEXITCODE -ne 0) {
        throw "Erreur lors du déploiement des index"
    }
    Write-Success "Index Firestore déployés"
} catch {
    Write-Error "Erreur lors du déploiement des index: $($_.Exception.Message)"
    Write-Host $indexResult -ForegroundColor Red
    Read-Host "Appuyez sur Entrée pour quitter"
    exit 1
}

# Étape 6: Vérification des index
Write-Info "Étape 6: Vérification des index..."
Write-Warning "Les index sont en cours de création (5-15 minutes)..."

try {
    firebase firestore:indexes
} catch {
    Write-Warning "Impossible de récupérer le statut des index"
}

# Résumé final
Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Success "DÉPLOIEMENT TERMINÉ"
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "📋 Actions suivantes:" -ForegroundColor White
Write-Host "1. Attendre la création des index (5-15 min)" -ForegroundColor White
Write-Host "2. Vérifier dans Firebase Console" -ForegroundColor White  
Write-Host "3. Recharger l'application" -ForegroundColor White
Write-Host "4. Tester les fonctionnalités" -ForegroundColor White
Write-Host ""

Write-Host "🔗 Firebase Console:" -ForegroundColor White
Write-Host "https://console.firebase.google.com/project/florasynth-a461d/firestore/indexes" -ForegroundColor Blue
Write-Host ""

Read-Host "Appuyez sur Entrée pour terminer"
